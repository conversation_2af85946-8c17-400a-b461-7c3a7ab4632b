


const tttrr = [
  {
    "headers": {
      "host": "webhook.mooly.vn",
      "user-agent": "rest-client/2.1.0 (linux-musl x86_64) ruby/3.4.4p34",
      "content-length": "4225",
      "accept": "application/json",
      "accept-encoding": "gzip;q=1.0,deflate;q=0.6,identity;q=0.3",
      "content-type": "application/json",
      "x-forwarded-for": "*********",
      "x-forwarded-host": "webhook.mooly.vn",
      "x-forwarded-port": "443",
      "x-forwarded-proto": "https",
      "x-forwarded-server": "7736946da0be",
      "x-real-ip": "*********"
    },
    "params": {},
    "query": {},
    "body": {
      "account": {
        "id": 213,
        "name": "<PERSON><PERSON><PERSON> Trần Admin"
      },
      "additional_attributes": {},
      "content_attributes": {
        "in_reply_to_external_id": null,
        "postback_payload": "DESIGN_CONSULTATION"
      },
      "content_type": "text",
      "content": "Add to cart",
      "conversation": {
        "additional_attributes": {},
        "can_reply": true,
        "channel": "Channel::FacebookPage",
        "contact_inbox": {
          "id": 24449,
          "contact_id": 24450,
          "inbox_id": 261,
          "source_id": "*****************",
          "created_at": "2025-06-09T11:23:59.267Z",
          "updated_at": "2025-06-09T11:23:59.267Z",
          "hmac_verified": false,
          "pubsub_token": "ZbsdeYMAkrVbD763jAqkvEHd"
        },
        "id": 4,
        "inbox_id": 261,
        "messages": [
          {
            "id": 288338,
            "content": "Add to cart",
            "account_id": 213,
            "inbox_id": 261,
            "conversation_id": 4,
            "message_type": 0,
            "created_at": **********,
            "updated_at": "2025-06-10T01:53:12.800Z",
            "private": false,
            "status": "sent",
            "source_id": "m_zEdaVVh3gi_gQ4PHM_bTNVIK79Q1GVCir1_sAcUU6HQyf3HnfEh7RHrg59kjJJrdtmmlER7gVO6HTZr7siEuTw",
            "content_type": "text",
            "content_attributes": {
              "in_reply_to_external_id": null,
              "postback_payload": "ITEM_SELECTED"
            },
            "sender_type": "Contact",
            "sender_id": 24450,
            "external_source_ids": {},
            "additional_attributes": {},
            "processed_message_content": "Add to cart",
            "sentiment": {},
            "conversation": {
              "assignee_id": null,
              "unread_count": 2,
              "last_activity_at": **********,
              "contact_inbox": {
                "source_id": "*****************"
              }
            },
            "sender": {
              "additional_attributes": {},
              "custom_attributes": {},
              "email": null,
              "id": 24450,
              "identifier": null,
              "name": "Hữu Nguyễn",
              "phone_number": null,
              "thumbnail": "https://app.mooly.vn/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBM2gwQVE9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--233882ee749f02dfc2472f193fc6b2e791c7005d/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--bff97024fce1aa27f64c605c0bde650dcf23499b/profilepic",
              "blocked": false,
              "type": "contact"
            }
          }
        ],
        "labels": [],
        "meta": {
          "sender": {
            "additional_attributes": {},
            "custom_attributes": {},
            "email": null,
            "id": 24450,
            "identifier": null,
            "name": "Hữu Nguyễn",
            "phone_number": null,
            "thumbnail": "https://app.mooly.vn/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBM2gwQVE9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--233882ee749f02dfc2472f193fc6b2e791c7005d/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--bff97024fce1aa27f64c605c0bde650dcf23499b/profilepic",
            "blocked": false,
            "type": "contact"
          },
          "assignee": null,
          "team": null,
          "hmac_verified": false
        },
        "status": "pending",
        "custom_attributes": {},
        "snoozed_until": null,
        "unread_count": 2,
        "first_reply_created_at": "2025-06-10T01:51:05.007Z",
        "priority": null,
        "waiting_since": **********,
        "agent_last_seen_at": **********,
        "contact_last_seen_at": 0,
        "last_activity_at": **********,
        "timestamp": **********,
        "created_at": **********,
        "updated_at": **********.8073175
      },
      "created_at": "2025-06-10T01:53:12.800Z",
      "id": 288338,
      "inbox": {
        "id": 261,
        "name": "Loma Bag - Xưởng Túi Vải In Logo"
      },
      "message_type": "incoming",
      "private": false,
      "sender": {
        "account": {
          "id": 213,
          "name": "Thọ Trần Admin"
        },
        "additional_attributes": {},
        "avatar": "https://app.mooly.vn/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBM2gwQVE9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--233882ee749f02dfc2472f193fc6b2e791c7005d/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--bff97024fce1aa27f64c605c0bde650dcf23499b/profilepic",
        "custom_attributes": {},
        "email": null,
        "id": 24450,
        "identifier": null,
        "name": "Hữu Nguyễn",
        "phone_number": null,
        "thumbnail": "https://app.mooly.vn/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBM2gwQVE9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--233882ee749f02dfc2472f193fc6b2e791c7005d/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--bff97024fce1aa27f64c605c0bde650dcf23499b/profilepic",
        "blocked": false
      },
      "source_id": "m_zEdaVVh3gi_gQ4PHM_bTNVIK79Q1GVCir1_sAcUU6HQyf3HnfEh7RHrg59kjJJrdtmmlER7gVO6HTZr7siEuTw",
      "event": "message_created"
    },
    "webhookUrl": "https://webhook.mooly.vn/webhook-test/78984a37-e5be-48dd-9039-a261e3f90158",
    "executionMode": "test"
  }
]