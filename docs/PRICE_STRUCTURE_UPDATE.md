# Cập nhật cấu trúc giá sản phẩm

## Vấn đề trước đây

Hệ thống trước đây có vấn đề về mapping giá sản phẩm từ các platform (Haravan, Sapo) vào database:

### Cấu trúc API Platform:
- **`price`**: <PERSON>i<PERSON> bán hiện tại (selling price)
- **`compare_at_price`**: Gi<PERSON> gốc để so sánh (original price)
- **Logic**: `compare_at_price` > `price` = có giảm giá

### Cấu trúc Database cũ (SAI):
- **`price`**: Được map từ `price` platform = giá bán
- **`compare_at_price`**: Được map từ `compare_at_price` platform = giá gốc

**Vấn đề**: Tên cột trong database không phù hợp với ý nghĩa thực tế!

## Gi<PERSON>i pháp đã thực hiện

### 1. Cập nhật cấu trúc Database

Đã thực hiện migration để đổi tên cột:

```sql
-- Cấu trúc mới
ALTER TABLE products 
  ADD COLUMN price NUMERIC DEFAULT 0 NOT NULL,      -- Giá gốc (từ compare_at_price platform)
  ADD COLUMN sale_price NUMERIC DEFAULT 0;          -- Giá bán (từ price platform)

ALTER TABLE product_variants 
  ADD COLUMN price NUMERIC DEFAULT 0 NOT NULL,      -- Giá gốc (từ compare_at_price platform)  
  ADD COLUMN sale_price NUMERIC DEFAULT 0;          -- Giá bán (từ price platform)
```

### 2. Cập nhật Adapter Logic

**Haravan Adapter** (`src/services/sync/adapters/haravan.adapter.ts`):
```typescript
// Sản phẩm chính
price: product.variants?.[0]?.compare_at_price || product.compare_at_price || product.variants?.[0]?.price || product.price || 0,
sale_price: product.variants?.[0]?.price || product.price || null,

// Variants
price: variant.compare_at_price || variant.price || 0,
sale_price: variant.price || null,
```

**Sapo Adapter** (`src/services/sync/adapters/sapo.adapter.ts`):
```typescript
// Tương tự Haravan
price: product.variants?.[0]?.compare_at_price || product.compare_at_price || product.variants?.[0]?.price || product.price || 0,
sale_price: product.variants?.[0]?.price || product.price || null,
```

### 3. Logic Tool vẫn đúng

Các tool trong `src/mastra/tools/product/` đã sử dụng logic đúng:

```typescript
const originalPrice = parseFloat(product.price);           // Giá gốc
const finalPrice = product.sale_price ? parseFloat(product.sale_price) : originalPrice;  // Giá bán
const isOnSale = product.sale_price && parseFloat(product.sale_price) < originalPrice;
```

## Kết quả

### Trước khi sửa:
```
Platform: price=18990000, compare_at_price=20990000
Database: price=18990000, compare_at_price=20990000  ❌ SAI
Tool hiển thị: Giá 20990000 VNĐ (Giảm từ 18990000 VNĐ)  ❌ SAI
```

### Sau khi sửa:
```
Platform: price=18990000, compare_at_price=20990000
Database: price=20990000, sale_price=18990000  ✅ ĐÚNG
Tool hiển thị: Giá 18990000 VNĐ (Giảm 10% từ 20990000 VNĐ)  ✅ ĐÚNG
```

## Ý nghĩa các trường

### Database Schema:
- **`price`**: Giá gốc của sản phẩm (original price)
- **`sale_price`**: Giá bán hiện tại (selling price, có thể null nếu không có sale)

### Platform API:
- **`price`**: Giá bán hiện tại 
- **`compare_at_price`**: Giá gốc để so sánh

### Tool Logic:
- **`original_price`**: Lấy từ `price` database
- **`final_price`**: Lấy từ `sale_price` database, fallback về `price` nếu null
- **`is_on_sale`**: `sale_price` < `price`

## Files đã cập nhật

1. **Database Migration**: 7 bước migration để cập nhật cấu trúc
2. **Haravan Adapter**: `src/services/sync/adapters/haravan.adapter.ts`
3. **Sapo Adapter**: `src/services/sync/adapters/sapo.adapter.ts`
4. **Test Script**: `test-price-sync.js` để verify logic

## Lưu ý quan trọng

- Tất cả sản phẩm hiện có đã được migrate với logic đúng
- Tools không cần thay đổi vì đã sử dụng logic đúng từ trước
- Hệ thống giờ đây hiển thị giá chính xác cho khách hàng
- Đồng bộ từ platform sẽ lưu giá đúng vào database
