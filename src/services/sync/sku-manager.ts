import { supabaseAdmin } from "../../config/supabase";


/**
 * Interface cho SKU counter
 */
interface SkuCounter {
  platform: string;
  tenant_id: string;
  current_counter: number;
  last_updated: string;
}

/**
 * Tạo SKU đơn giản dựa trên platform và source_id
 * @param platform Tên nền tảng (haravan, sapo)
 * @param sourceId ID nguồn từ nền tảng
 * @returns SKU đã được tạo
 */
const generateSimpleSku = (platform: string, sourceId: string): string => {
  const platformPrefixes: { [key: string]: string } = {
    'haravan': 'HARA',
    'sapo': 'SAPO'
  };

  const prefix = platformPrefixes[platform.toLowerCase()] || platform.toUpperCase().substring(0, 4);
  const paddedId = sourceId.toString().padStart(5, '0');

  return `${prefix}${paddedId}`;
};

/**
 * Tạo SKU đơn giản dựa trên platform và sourceId
 * @param platform Tên nền tảng (haravan, sapo)
 * @param tenant_id ID của tenant
 * @param sourceId ID nguồn từ nền tảng (bắt buộc)
 * @returns SKU đã được tạo
 */
export const generateNextSku = async (platform: string, tenant_id: string, sourceId: string): Promise<string> => {
  try {
    // Tạo SKU đơn giản từ platform và sourceId
    const newSku = generateSimpleSku(platform, sourceId);

    // Kiểm tra xem SKU đã tồn tại chưa trong cùng tenant
    const { data: existingProduct } = await supabaseAdmin
      .from('products')
      .select('id')
      .eq('sku', newSku)
      .eq('tenant_id', tenant_id)
      .single();

    if (existingProduct) {
      // Nếu SKU đã tồn tại, thêm timestamp để tạo SKU unique
      const timestamp = Date.now().toString().slice(-3);
      return `${newSku}-${timestamp}`;
    }

    return newSku;
  } catch (error) {
    console.error(`Lỗi khi tạo SKU cho ${platform}:`, error);

    // Fallback: tạo SKU với timestamp
    const timestamp = Date.now().toString().slice(-5);
    const prefix = platform.toUpperCase().substring(0, 4);
    return `${prefix}${timestamp}`;
  }
};

/**
 * Tạo SKU cho biến thể dựa trên SKU sản phẩm cha
 * @param productSku SKU của sản phẩm cha
 * @param variantIndex Chỉ số của biến thể
 * @param variantAttributes Thuộc tính của biến thể
 * @returns SKU của biến thể
 */
export const generateVariantSku = (productSku: string, variantIndex: number, variantAttributes?: Record<string, string>): string => {
  // Tạo suffix từ thuộc tính biến thể
  let suffix = '';
  if (variantAttributes && Object.keys(variantAttributes).length > 0) {
    const attributeValues = Object.values(variantAttributes)
      .map(value => value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase())
      .join('');
    suffix = attributeValues.substring(0, 3); // Lấy 3 ký tự đầu
  }
  
  if (!suffix) {
    // Nếu không có thuộc tính, sử dụng chỉ số biến thể
    suffix = (variantIndex + 1).toString().padStart(2, '0');
  }
  
  return `${productSku}-${suffix}`;
};

/**
 * Kiểm tra và chuẩn hóa SKU hiện có
 * @param sku SKU cần kiểm tra
 * @param platform Tên nền tảng
 * @param tenant_id ID của tenant
 * @param sourceId ID nguồn (bắt buộc)
 * @returns SKU đã được chuẩn hóa
 */
export const validateAndNormalizeSku = async (sku: string, platform: string, tenant_id: string, sourceId: string): Promise<string> => {
  // Nếu có SKU và không rỗng, sử dụng SKU gốc
  if (sku && sku.trim() !== '') {
    return sku.trim();
  }

  // Nếu SKU rỗng hoặc không có, tạo SKU từ sourceId
  return generateNextSku(platform, tenant_id, sourceId);
};

/**
 * Khởi tạo SKU counters - không cần thiết nữa vì không sử dụng database counter
 * @deprecated Không cần sử dụng nữa
 */
export const initializeSkuCounters = async (): Promise<void> => {
  // Không cần khởi tạo gì cả vì không sử dụng sku_counters table nữa
  console.log('SKU counters không cần khởi tạo - sử dụng SKU từ source');
};

export default {
  generateNextSku,
  generateVariantSku,
  validateAndNormalizeSku,
  initializeSkuCounters
};
