/**
 * Message Batch Processor - <PERSON><PERSON> lý các job message batching
 * <PERSON><PERSON><PERSON> hợp với Agent Service để gửi phản hồi tổng hợp
 */
import { Job } from 'bullmq';
import { MessageBatchJobData, MessageBatch, BufferedMessage } from '../message-buffer.service';
import { messageBufferService } from '../message-buffer.service';
import { generateAgentResponse } from '../../agent.service';
import { checkTenantCredit, deductTenantCredit } from '../../credit.service';
import { getMoolyAccount } from '../../postgres/chatbot.service';
import { searchProducts } from '../../weaviate/product.service';
import { getProductDetailsByIds } from '../../supabase/product.service';
import { extractFinalAnswer } from '../../../utils/response.utils';
import { createFollowUpJob, cancelFollowUpJobs } from '../follow-up.service';
import dotenv from 'dotenv';

dotenv.config();

/**
 * <PERSON><PERSON> lý message batch job
 */
export const processMessageBatch = async (job: Job<MessageBatchJobData>) => {
  const { batchKey, batch, immediate } = job.data;

  console.log(`🔄 Bắt đầu xử lý batch ${batchKey} (${batch.messages.length} tin nhắn)${immediate ? ' - IMMEDIATE' : ''}`);

  try {
    // Lấy batch mới nhất từ Redis (có thể đã có tin nhắn mới)
    const latestBatch = await messageBufferService.getBatch(batchKey);
    const batchToProcess = latestBatch || batch;

    if (batchToProcess.messages.length === 0) {
      console.log(`⚠️ Batch ${batchKey} không có tin nhắn, bỏ qua`);
      return { success: true, processed: 0 };
    }

    // Kiểm tra credit trước khi xử lý
    try {
      const creditCheck = await checkTenantCredit(batchToProcess.tenantId);
      if (!creditCheck.success || !creditCheck.hasCredit) {
        console.log(`⚠️ Tenant ${batchToProcess.tenantId} không đủ credit, bỏ qua batch`);
        await messageBufferService.clearBatch(batchKey);
        return { success: false, error: 'Insufficient credit' };
      }
    } catch (creditError) {
      console.error('❌ Lỗi khi kiểm tra credit:', creditError);
      // Tắt typing indicator nếu đã bật
      try {
        await setTypingStatus(batchToProcess, 'off');
      } catch (typingError) {
        console.error('❌ Lỗi khi tắt typing indicator:', typingError);
      }
      return { success: false, error: 'Credit check error' };
    }

    // Bật typing indicator
    let typingStarted = false;
    try {
      await setTypingStatus(batchToProcess, 'on');
      typingStarted = true;
    } catch (typingError) {
      console.error('❌ Lỗi khi bật typing indicator:', typingError);
    }

    let response = null;
    let conversationContext = null;

    try {
      // Tạo conversation context từ tất cả tin nhắn trong batch
      conversationContext = await buildConversationContext(batchToProcess);
      console.log(conversationContext);

      // Lấy contact name từ metadata của tin nhắn đầu tiên (hoặc bất kỳ tin nhắn nào có metadata)
      let contactName = undefined;
      for (const message of batchToProcess.messages) {
        if (message.metadata?.contactName) {
          contactName = message.metadata.contactName;
          break;
        }
      }

      // Gọi Agent Service để tạo phản hồi tổng hợp
      response = await generateAgentResponse(
        conversationContext,
        batchToProcess.threadId,
        batchToProcess.resourceId,
        batchToProcess.botId,
        batchToProcess.tenantId,
        {
          channel_type: 'bot',
          instruction: batchToProcess.chatbotInfo?.instruction?.instruction || null,
          type: batchToProcess.chatbotInfo?.instruction?.type || 'sale_bot',
        },
        batchToProcess.accountId,
        batchToProcess.conversationId,
        contactName
      );
    } catch (agentError) {
      console.error('❌ Lỗi khi tạo phản hồi từ agent:', agentError);

      // Tắt typing indicator
      if (typingStarted) {
        try {
          await setTypingStatus(batchToProcess, 'off');
        } catch (typingError) {
          console.error('❌ Lỗi khi tắt typing indicator:', typingError);
        }
      }

      // Xóa batch và return lỗi mà không gửi tin nhắn hoặc trừ credit
      await messageBufferService.clearBatch(batchKey);
      return { success: false, error: 'Agent response error', processed: 0 };
    }

    // Gửi tin nhắn cho khách hàng nếu có response
    if (response?.text) {
      try {
        // Extract chỉ nội dung final_answer để gửi cho khách hàng
        const finalAnswerContent = extractFinalAnswer(response.text);
        await sendMessageToCustomer(batchToProcess, finalAnswerContent);

        // Chỉ trừ credit sau khi gửi tin nhắn thành công
        try {
          await deductTenantCredit({
            tenant_id: batchToProcess.tenantId,
            creditAmount: 1,
            referenceType: 'message_batch_response',
            description: `Phản hồi batch ${batchToProcess.messages.length} tin nhắn`,
          });
          console.log(`💳 Đã trừ 1 credit cho tenant ${batchToProcess.tenantId}`);
        } catch (creditError) {
          console.error('❌ Lỗi khi trừ credit:', creditError);
          // Không throw error để không làm fail job vì tin nhắn đã được gửi thành công
        }
      } catch (sendError) {
        console.error('❌ Lỗi khi gửi tin nhắn cho khách hàng:', sendError);

        // Tắt typing indicator
        if (typingStarted) {
          try {
            await setTypingStatus(batchToProcess, 'off');
          } catch (typingError) {
            console.error('❌ Lỗi khi tắt typing indicator:', typingError);
          }
        }

        // Xóa batch và return lỗi mà không trừ credit vì tin nhắn không được gửi
        await messageBufferService.clearBatch(batchKey);
        return { success: false, error: 'Message send error', processed: 0 };
      }
    }

    // Tắt typing indicator
    if (typingStarted) {
      try {
        await setTypingStatus(batchToProcess, 'off');
      } catch (typingError) {
        console.error('❌ Lỗi khi tắt typing indicator:', typingError);
      }
    }

    // Xóa batch khỏi Redis sau khi xử lý thành công
    await messageBufferService.clearBatch(batchKey);

    // Xử lý follow-up automation sau khi gửi tin nhắn thành công
    await handleFollowUpAutomation(batchToProcess);

    return {
      success: true,
      processed: batchToProcess.messages.length,
      response: response?.text || null,
      immediate,
    };

  } catch (error) {
    console.error(`❌ Lỗi không mong muốn khi xử lý batch ${batchKey}:`, error);

    // Tắt typing indicator trong trường hợp lỗi không mong muốn
    try {
      await setTypingStatus(batch, 'off');
    } catch (typingError) {
      console.error('❌ Lỗi khi tắt typing indicator:', typingError);
    }

    // Xóa batch để tránh retry vô hạn
    try {
      await messageBufferService.clearBatch(batchKey);
    } catch (clearError) {
      console.error('❌ Lỗi khi xóa batch:', clearError);
    }

    // Return error thay vì throw để tránh retry
    return { success: false, error: 'Unexpected error', processed: 0 };
  }
};

/**
 * Xây dựng conversation context từ batch messages với logic tìm kiếm sản phẩm cho hình ảnh
 *
 * Cấu hình mới:
 * - Tin nhắn hình ảnh delay 10s mặc định để chờ user nhập thêm câu hỏi
 * - Thông tin sản phẩm được gán vai trò assistant (nhân viên tìm được thông tin)
 * - Tự động tạo câu hỏi tư vấn nếu user không nhập text
 */
async function buildConversationContext(batch: MessageBatch): Promise<any[]> {
  const context: any[] = [];

  // Mảng lưu trữ tất cả các product_id tìm được từ hình ảnh
  const allProductIds: string[] = [];

  // Gom tất cả tin nhắn text để tạo context tổng hợp
  const allTextMessages: string[] = [];

  // Xử lý từng tin nhắn để tìm kiếm sản phẩm từ hình ảnh
  for (const message of batch.messages) {
    // Gom tin nhắn text
    if (message.content && message.content.trim()) {
      allTextMessages.push(message.content.trim());
    }

    // Xử lý hình ảnh để tìm kiếm sản phẩm
    if (message.attachments && message.attachments.length > 0) {
      for (const attachment of message.attachments) {
        const imageUrl = attachment.data_url || attachment.thumb_url;

        if (imageUrl) {
          console.log('🔍 Tìm kiếm sản phẩm bằng hình ảnh:', imageUrl);

          try {
            // Tìm kiếm sản phẩm bằng hình ảnh thông qua Weaviate
            const searchResult = await searchProducts(
              {
                query: "query", // Không cần query text khi tìm bằng hình ảnh
                image_url: imageUrl,
                tenant_id: batch.tenantId,
                bot_id: batch.botId,
              },
              1 // Giới hạn 1 kết quả cho mỗi hình ảnh
            );

            if (searchResult.success && searchResult.data.objects && searchResult.data.objects.length > 0) {
              // Lấy product_id từ kết quả tìm kiếm và thêm vào mảng
              const productId = searchResult.data.objects[0]?.properties?.product_id;
              if (productId && !allProductIds.includes(productId)) {
                allProductIds.push(productId);
                console.log(`✅ Thêm product_id: ${productId} vào danh sách tìm kiếm`);
              }
            }
          } catch (error) {
            console.error('❌ Lỗi khi tìm kiếm sản phẩm bằng hình ảnh:', error);
          }
        }
      }
    }
  }

  // Lấy thông tin chi tiết sản phẩm nếu tìm thấy
  let productDetails = null;
  if (allProductIds.length > 0) {
    console.log(`📦 Lấy thông tin chi tiết cho ${allProductIds.length} sản phẩm`);

    try {
      const productResult = await getProductDetailsByIds({
        productIds: allProductIds.slice(0, 3), // Giới hạn tối đa 3 sản phẩm
        tenant_id: batch.tenantId,
      });

      if (productResult.success && productResult.data && productResult.data.length > 0) {
        productDetails = productResult.data;
        console.log(`✅ Lấy được thông tin ${productDetails.length} sản phẩm`);
      }
    } catch (error) {
      console.error('❌ Lỗi khi lấy thông tin chi tiết sản phẩm:', error);
    }
  }

  // Xây dựng conversation context với vai trò rõ ràng
  if (productDetails && productDetails.length > 0) {
    // Thêm thông tin sản phẩm với vai trò assistant (nhân viên tìm được thông tin)
    let productInfo = 'Tôi đã tìm thấy thông tin sản phẩm từ hình ảnh bạn gửi:\n\n';
    productDetails.forEach((product: any, index: number) => {
      productInfo += `${index + 1}. **${product.name}**\n`;
      productInfo += `   - Giá: ${product.price ? new Intl.NumberFormat('vi-VN').format(product.price) + ' VNĐ' : 'Liên hệ'}\n`;
      if (product.sku) productInfo += `   - SKU: ${product.sku}\n`;
      if (product.description) productInfo += `   - Mô tả: ${product.description.substring(0, 200)}${product.description.length > 200 ? '...' : ''}\n`;
      if (product.images && product.images.length > 0) {
        productInfo += `   - Hình ảnh: ${product.images.slice(0, 3).map((img: any) => img.url).join(', ')}\n`;
      }
      productInfo += '\n';
    });

    // Thêm thông tin sản phẩm với vai trò assistant
    context.push({
      role: 'assistant',
      content: productInfo,
    });

    // Xử lý tin nhắn text của khách hàng
    if (allTextMessages.length > 0) {
      // Có tin nhắn text từ khách hàng
      context.push({
        role: 'user',
        content: allTextMessages.join(' '),
      });
    } else {
      // Không có tin nhắn text, tạo câu hỏi mặc định để tư vấn
      context.push({
        role: 'user',
        content: 'Tư vấn cho tôi sản phẩm trên',
      });
    }
  } else {
    // Không tìm thấy sản phẩm từ hình ảnh
    let textContent = '';
    if (allTextMessages.length > 0) {
      textContent = allTextMessages.join(' ');
    } else {
      textContent = 'Tôi đang tìm kiếm sản phẩm trong hình ảnh này, bạn có thể giúp tôi không?';
    }

    context.push({
      role: 'user',
      content: textContent,
    });
  }

  return context;
}

/**
 * Gửi tin nhắn cho khách hàng
 */
async function sendMessageToCustomer(batch: MessageBatch, messageContent: string): Promise<void> {
  try {
    // Lấy API token từ account
    let apiToken = process.env.MOOLY_BOT_API_KEY || '';

    try {
      const accountResult = await getMoolyAccount({
        accoutnId: batch.accountId,
      });
      if (accountResult.success && accountResult.data?.token) {
        apiToken = accountResult.data.token;
        console.log(`🔑 Sử dụng token từ tài khoản: ${apiToken.substring(0, 10)}...`);
      }
    } catch (error) {
      console.warn('⚠️ Không thể lấy token từ account, sử dụng token mặc định');
    }

    const response = await fetch(
      `https://app.mooly.vn/api/v1/accounts/${batch.accountId}/conversations/${batch.conversationId}/messages`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          api_access_token: apiToken,
        },
        body: JSON.stringify({
          content: messageContent,
          private: false,
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Không thể gửi tin nhắn cho khách hàng:`, response.status, errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    console.log(`📤 Đã gửi tin nhắn cho khách hàng: "${messageContent.substring(0, 50)}${messageContent.length > 50 ? '...' : ''}"`);
  } catch (error) {
    console.error('❌ Lỗi khi gửi tin nhắn cho khách hàng:', error);
    throw error; // Re-throw để BullMQ có thể retry
  }
}

/**
 * Bật/tắt typing indicator
 */
async function setTypingStatus(batch: MessageBatch, status: 'on' | 'off'): Promise<void> {
  try {
    const apiToken = process.env.MOOLY_BOT_API_KEY || '';

    const response = await fetch(
      `https://app.mooly.vn/api/v1/accounts/${batch.accountId}/conversations/${batch.conversationId}/toggle_typing_status`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          api_access_token: apiToken,
        },
        body: JSON.stringify({
          typing_status: status,
          is_private: false,
        }),
      }
    );

    if (!response.ok) {
      console.warn(`⚠️ Không thể ${status === 'on' ? 'bật' : 'tắt'} typing indicator:`, response.statusText);
    }
  } catch (error) {
    console.warn(`⚠️ Lỗi khi ${status === 'on' ? 'bật' : 'tắt'} typing indicator:`, error);
  }
}

/**
 * Fallback processor cho các job failed
 * Đảm bảo không gửi tin nhắn và không trừ credit khi có lỗi
 */
export const handleFailedMessageBatch = async (job: Job<MessageBatchJobData>, error: Error) => {
  const { batchKey, batch } = job.data;

  console.error(`❌ Job failed cho batch ${batchKey}:`, error);
  console.log(`⚠️ Không gửi tin nhắn cho khách hàng và không trừ credit do lỗi xử lý`);

  try {
    // Tắt typing indicator
    await setTypingStatus(batch, 'off');

    // Luôn xóa batch khi job failed để tránh retry và memory leak
    await messageBufferService.clearBatch(batchKey);
    console.log(`🧹 Đã xóa batch ${batchKey} sau lỗi xử lý`);

  } catch (cleanupError) {
    console.error('❌ Lỗi khi cleanup failed job:', cleanupError);
  }

  // Không throw error để tránh retry vô hạn
  // Job sẽ được đánh dấu là failed nhưng không retry
};

/**
 * Xử lý follow-up automation sau khi bot gửi tin nhắn
 * Logic: Hủy job follow-up hiện tại và tạo job mới từ đầu
 */
async function handleFollowUpAutomation(batch: MessageBatch): Promise<void> {
  try {
    const conversationId = batch.conversationId;
    const tenantId = batch.tenantId;
    const accountId = batch.accountId;

    // Lấy channel_id từ chatbotInfo
    const channelId = batch.chatbotInfo?.channel?.id;

    if (!channelId) {
      console.log(`⚠️ Không tìm thấy channel_id cho conversation ${conversationId}, bỏ qua follow-up`);
      return;
    }

    console.log(`🔄 Xử lý follow-up automation cho conversation ${conversationId}, channel ${channelId}`);

    // Bước 1: Hủy tất cả follow-up jobs hiện tại (nếu có)
    const cancelResult = await cancelFollowUpJobs({
      conversation_id: conversationId,
      tenant_id: tenantId,
      reason: 'Bot response sent - reset follow-up sequence',
    });

    if (cancelResult.success) {
      console.log(`✅ Đã hủy follow-up jobs cũ cho conversation ${conversationId}`);
    }

    // Bước 2: Tạo follow-up job mới từ rule đầu tiên
    const createResult = await createFollowUpJob({
      conversation_id: conversationId,
      tenant_id: tenantId,
      channel_id: channelId,
      account_id: accountId,
      rule_index: 0, // Bắt đầu từ rule đầu tiên
      reset_state: true, // Reset state về trạng thái ban đầu
    });

    if (createResult.success) {
      console.log(`✅ Đã tạo follow-up job mới cho conversation ${conversationId}: ${createResult.data?.jobId}`);
    } else {
      console.log(`⚠️ Không thể tạo follow-up job cho conversation ${conversationId}: ${createResult.error}`);
    }

  } catch (error: any) {
    console.error('❌ Lỗi khi xử lý follow-up automation:', error);
    // Không throw error để không làm fail message batch job
  }
}

export default {
  processMessageBatch,
  handleFailedMessageBatch,
};