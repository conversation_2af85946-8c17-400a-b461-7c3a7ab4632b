/**
 * Bull Board Service - Web Dashboard để monitor BullMQ queues
 * <PERSON>ung cấp real-time monitoring cho tất cả queues trong hệ thống
 */
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Queue } from 'bullmq';
import { QUEUE_NAMES, redisConnection, queueConfigs } from './config';

// Express adapter cho Bull Board
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

// Khởi tạo tất cả queues để monitor - sử dụng cùng config với workers
const queues = {
  [QUEUE_NAMES.PRODUCT_SYNC]: new Queue(QUEUE_NAMES.PRODUCT_SYNC, {
    ...queueConfigs[QUEUE_NAMES.PRODUCT_SYNC],
  }),
  [QUEUE_NAMES.EMAIL_NOTIFICATION]: new Queue(QUEUE_NAMES.EMAIL_NOTIFICATION, {
    ...queueConfigs[QUEUE_NAMES.EMAIL_NOTIFICATION],
  }),
  [QUEUE_NAMES.DATA_CLEANUP]: new Queue(QUEUE_NAMES.DATA_CLEANUP, {
    connection: redisConnection,
  }),
  [QUEUE_NAMES.WEBHOOK_PROCESSING]: new Queue(QUEUE_NAMES.WEBHOOK_PROCESSING, {
    connection: redisConnection,
  }),
  [QUEUE_NAMES.MESSAGE_BATCHING]: new Queue(QUEUE_NAMES.MESSAGE_BATCHING, {
    ...queueConfigs[QUEUE_NAMES.MESSAGE_BATCHING],
  }),
  [QUEUE_NAMES.FOLLOW_UP]: new Queue(QUEUE_NAMES.FOLLOW_UP, {
    ...queueConfigs[QUEUE_NAMES.FOLLOW_UP],
  }),
};

// Tạo adapters với custom formatters
const createQueueAdapter = (queue: Queue, queueName: string) => {
  const adapter = new BullMQAdapter(queue);
  
  // Custom formatter để hiển thị job name đẹp hơn
  adapter.setFormatter('name', (job) => {
    return `${job.name} #${job.id}`;
  });
  
  // Custom formatter để hiển thị data quan trọng
  adapter.setFormatter('data', (job) => {
    if (job.name === 'process-message-batch') {
      return {
        batchKey: job.data.batchKey,
        messageCount: job.data.batch?.messages?.length || 0,
        immediate: job.data.immediate,
        tenantId: job.data.batch?.tenantId,
      };
    }
    
    if (job.name === 'sync-sapo-products' || job.name === 'sync-haravan-products') {
      return {
        tenant_id: job.data.tenant_id,
        bot_id: job.data.bot_id,
        options: job.data.options,
        platform: job.name.includes('sapo') ? 'Sapo' : 'Haravan',
      };
    }
    
    return job.data;
  });

  return adapter;
};

// Tạo Bull Board với tất cả queues và custom config
const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
  queues: Object.entries(queues).map(([name, queue]) => createQueueAdapter(queue, name)),
  serverAdapter: serverAdapter,
  options: {
    uiConfig: {
      boardTitle: 'Mooly Chatbot AI - Queue Dashboard',
      boardLogo: {
        path: 'https://via.placeholder.com/100x50/4CAF50/FFFFFF?text=Mooly',
        width: '100px',
        height: '50px',
      },
      miscLinks: [
        { text: '🏥 System Health', url: '/health' },
        { text: '📊 Metrics', url: '/metrics' },
        { text: '🔄 API Docs', url: '/api' },
      ],
      favIcon: {
        default: 'https://via.placeholder.com/32x32/4CAF50/FFFFFF?text=M',
        alternative: 'https://via.placeholder.com/32x32/4CAF50/FFFFFF?text=M',
      },
    },
  },
});

/**
 * Lấy Express router cho Bull Board
 */
export const getBullBoardRouter = () => {
  return serverAdapter.getRouter();
};

/**
 * Thêm queue mới vào Bull Board
 */
export const addQueueToBoard = (queueName: string, queue: Queue) => {
  const adapter = createQueueAdapter(queue, queueName);
  addQueue(adapter);
  console.log(`✅ Đã thêm queue ${queueName} vào Bull Board`);
};

/**
 * Xóa queue khỏi Bull Board
 */
export const removeQueueFromBoard = (queueName: string) => {
  // Note: Bull Board không có direct method để remove by name
  // Cần implement custom logic nếu cần
  console.log(`⚠️ Remove queue ${queueName} from Bull Board (manual implementation needed)`);
};

/**
 * Lấy thống kê tổng quan của tất cả queues
 */
export const getAllQueuesStats = async () => {
  const stats: Record<string, any> = {};
  
  for (const [queueName, queue] of Object.entries(queues)) {
    try {
      const jobCounts = await queue.getJobCounts();
      
      stats[queueName] = {
        ...jobCounts,
        total: Object.values(jobCounts).reduce((sum: number, count: any) => sum + (count || 0), 0),
        queueName,
        isPaused: await queue.isPaused(),
      };
    } catch (error) {
      console.error(`❌ Lỗi khi lấy stats cho queue ${queueName}:`, error);
      stats[queueName] = {
        error: 'Unable to fetch stats',
        queueName,
      };
    }
  }

  return stats;
};

/**
 * Cleanup tất cả completed jobs (chạy định kỳ)
 */
export const cleanupAllQueues = async (keepJobs: number = 100) => {
  const results: Record<string, any> = {};
  
  for (const [queueName, queue] of Object.entries(queues)) {
    try {
      const cleanedCompleted = await queue.clean(0, keepJobs, 'completed');
      const cleanedFailed = await queue.clean(0, 50, 'failed'); // Giữ ít failed jobs hơn
      
      results[queueName] = {
        success: true,
        cleanedCompleted,
        cleanedFailed,
        message: `Cleaned ${cleanedCompleted} completed jobs, ${cleanedFailed} failed jobs`,
      };
    } catch (error) {
      console.error(`❌ Lỗi khi cleanup queue ${queueName}:`, error);
      results[queueName] = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  return results;
};

/**
 * Pause tất cả queues (emergency stop)
 */
export const pauseAllQueues = async () => {
  const results: Record<string, any> = {};
  
  for (const [queueName, queue] of Object.entries(queues)) {
    try {
      await queue.pause();
      results[queueName] = { success: true, status: 'paused' };
    } catch (error) {
      results[queueName] = { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  return results;
};

/**
 * Resume tất cả queues
 */
export const resumeAllQueues = async () => {
  const results: Record<string, any> = {};
  
  for (const [queueName, queue] of Object.entries(queues)) {
    try {
      await queue.resume();
      results[queueName] = { success: true, status: 'resumed' };
    } catch (error) {
      results[queueName] = { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  return results;
};

/**
 * Lấy queue instance theo tên
 */
export const getQueue = (queueName: string): Queue | null => {
  return queues[queueName as keyof typeof queues] || null;
};

/**
 * Kiểm tra health của Bull Board
 */
export const getBullBoardHealth = async () => {
  try {
    const stats = await getAllQueuesStats();
    const totalJobs = Object.values(stats).reduce((sum: number, stat: any) => 
      sum + (stat.total || 0), 0
    );
    
    return {
      status: 'healthy',
      totalQueues: Object.keys(queues).length,
      totalJobs,
      queues: stats,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    };
  }
};

// Export queues để sử dụng ở nơi khác
export { queues };

// Export default
export default {
  getBullBoardRouter,
  addQueueToBoard,
  removeQueueFromBoard,
  getAllQueuesStats,
  cleanupAllQueues,
  pauseAllQueues,
  resumeAllQueues,
  getQueue,
  getBullBoardHealth,
  queues,
};

console.log('🎛️ Bull Board Service initialized');
console.log(`📊 Dashboard available at: /admin/queues`);
console.log(`🔍 Monitoring ${Object.keys(queues).length} queues:`, Object.keys(queues)); 