/**
 * <PERSON><PERSON><PERSON> nghĩa và quản lý các Queue cho BullMQ - Tối <PERSON>u hóa
 */
import { Queue, Job } from 'bullmq';
import {
  QUEUE_NAMES,
  queueConfigs,
  loggingConfig,
  redisConnection
} from './config';
import { initializeFollowUpQueue, closeFollowUpQueue } from './follow-up.service';

// Interface cho Job Data
interface HaravanSyncJobData {
  token: string;
  tenant_id: string;
  bot_id: string;
  options?: {
    force?: boolean;
    limit?: number;
    page?: number;
    updated_at_min?: string;
    full_sync?: boolean;
    force_update?: boolean;
  };
}

interface SapoSyncJobData {
  sapo_url: string;
  tenant_id: string;
  bot_id: string;
  options?: {
    force?: boolean;
    limit?: number;
    page?: number;
    updated_at_min?: string;
    full_sync?: boolean;
    force_update?: boolean;
  };
}

// Map để lưu trữ tất cả queues
const queues = new Map<string, Queue>();

// Logging utility
const log = (level: 'info' | 'warn' | 'error', message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  const logData = data && loggingConfig.logJobData ? JSON.stringify(data) : '';
  
  switch (level) {
    case 'info':
      if (loggingConfig.logLevel === 'info') {
        console.log(`[${timestamp}] ℹ️ ${message}`, logData);
      }
      break;
    case 'warn':
      console.warn(`[${timestamp}] ⚠️ ${message}`, logData);
      break;
    case 'error':
      console.error(`[${timestamp}] ❌ ${message}`, logData);
      break;
  }
};

/**
 * Tạo queue với cấu hình tối ưu
 */
const createQueue = (queueName: string): Queue => {
  if (queues.has(queueName)) {
    return queues.get(queueName)!;
  }

  const config = queueConfigs[queueName as keyof typeof queueConfigs] || queueConfigs[QUEUE_NAMES.PRODUCT_SYNC];
  const queue = new Queue(queueName, config);

  // Event listeners cho queue
  queue.on('error', (error) => {
    log('error', `❌ Lỗi queue ${queueName}:`, error);
  });

  // Note: Queue events như 'waiting', 'active', 'completed', 'failed', 'stalled'
  // được handle ở Worker level, không phải Queue level

  queues.set(queueName, queue);
  log('info', `📋 Queue ${queueName} đã được tạo`);

  return queue;
};

// Khởi tạo Product Sync Queue
export const productSyncQueue = createQueue(QUEUE_NAMES.PRODUCT_SYNC);

// Khởi tạo Follow-up Queue
export const followUpQueue = initializeFollowUpQueue();

/**
 * Thêm job đồng bộ sản phẩm từ Haravan vào queue
 */
export const addHaravanSyncJob = async (
  token: string,
  tenant_id: string,
  bot_id: string,
  options?: HaravanSyncJobData['options']
): Promise<Job> => {
  try {
    const jobData: HaravanSyncJobData = {
      token,
      tenant_id,
      bot_id,
      options: options || {}
    };

    const job = await productSyncQueue.add(
      'sync-haravan-products',
      jobData,
      {
        // Tùy chỉnh job options cho Haravan sync
        priority: 10, // Ưu tiên cao
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
        removeOnComplete: 10,
        removeOnFail: 20,
      }
    );

    log('info', `📤 Đã thêm Haravan sync job vào queue`, {
      jobId: job.id,
      tenant_id,
      bot_id,
      options,
    });

    return job;
  } catch (error: any) {
    log('error', 'Lỗi khi thêm job đồng bộ Haravan vào queue:', error);
    throw error;
  }
};

/**
 * Thêm job đồng bộ sản phẩm từ Sapo vào queue
 */
export const addSapoSyncJob = async (
  sapo_url: string,
  tenant_id: string,
  bot_id: string,
  options?: SapoSyncJobData['options']
): Promise<Job> => {
  try {
    const jobData: SapoSyncJobData = {
      sapo_url,
      tenant_id,
      bot_id,
      options: options || {}
    };

    const job = await productSyncQueue.add(
      'sync-sapo-products',
      jobData,
      {
        // Tùy chỉnh job options cho Sapo sync
        priority: 10, // Ưu tiên cao
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
        removeOnComplete: 10,
        removeOnFail: 20,
      }
    );

    log('info', `📤 Đã thêm Sapo sync job vào queue`, {
      jobId: job.id,
      tenant_id,
      bot_id,
      sapo_url,
      options,
    });

    return job;
  } catch (error: any) {
    log('error', 'Lỗi khi thêm job đồng bộ Sapo vào queue:', error);
    throw error;
  }
};

/**
 * Lấy queue theo tên
 */
export const getQueue = (queueName: string): Queue | null => {
  return queues.get(queueName) || null;
};

/**
 * Lấy thống kê của tất cả queues
 */
export const getQueuesStats = async () => {
  const stats: Record<string, any> = {};

  for (const [queueName, queue] of queues) {
    try {
      const jobCounts = await queue.getJobCounts();
      const workers = await queue.getWorkers();
      
      stats[queueName] = {
        jobCounts,
        workersCount: workers.length,
        isPaused: await queue.isPaused(),
      };
    } catch (error) {
      log('error', `Lỗi khi lấy stats cho queue ${queueName}:`, error);
      stats[queueName] = { error: 'Unable to fetch stats' };
    }
  }

  return stats;
};

/**
 * Tạm dừng queue
 */
export const pauseQueue = async (queueName: string): Promise<boolean> => {
  try {
    const queue = queues.get(queueName);
    if (!queue) {
      log('warn', `Không tìm thấy queue: ${queueName}`);
      return false;
    }

    await queue.pause();
    log('info', `⏸️ Queue ${queueName} đã được tạm dừng`);
    return true;
  } catch (error) {
    log('error', `Lỗi khi tạm dừng queue ${queueName}:`, error);
    return false;
  }
};

/**
 * Tiếp tục queue
 */
export const resumeQueue = async (queueName: string): Promise<boolean> => {
  try {
    const queue = queues.get(queueName);
    if (!queue) {
      log('warn', `Không tìm thấy queue: ${queueName}`);
      return false;
    }

    await queue.resume();
    log('info', `▶️ Queue ${queueName} đã được tiếp tục`);
    return true;
  } catch (error) {
    log('error', `Lỗi khi tiếp tục queue ${queueName}:`, error);
    return false;
  }
};

/**
 * Xóa tất cả jobs trong queue
 */
export const cleanQueue = async (
  queueName: string, 
  grace: number = 0,
  type: 'completed' | 'wait' | 'active' | 'delayed' | 'failed' = 'completed'
): Promise<number> => {
  try {
    const queue = queues.get(queueName);
    if (!queue) {
      log('warn', `Không tìm thấy queue: ${queueName}`);
      return 0;
    }

    const deletedJobs = await queue.clean(grace, 100, type);
    log('info', `🧹 Đã xóa ${deletedJobs.length} jobs loại ${type} trong queue ${queueName}`);
    return deletedJobs.length;
  } catch (error) {
    log('error', `Lỗi khi clean queue ${queueName}:`, error);
    return 0;
  }
};

/**
 * Đóng tất cả queues
 */
export const closeAllQueues = async (): Promise<void> => {
  try {
    const closePromises: Promise<void>[] = [];
    
    for (const [queueName, queue] of queues) {
      log('info', `Đang đóng queue: ${queueName}`);
      closePromises.push(
        queue.close().then(() => {
          log('info', `✅ Queue ${queueName} đã được đóng`);
        })
      );
    }
    
    await Promise.all(closePromises);
    queues.clear();

    // Đóng follow-up Redis connection
    await closeFollowUpQueue();

    log('info', '✅ Tất cả queues đã được đóng');
  } catch (error) {
    log('error', '❌ Lỗi khi đóng queues:', error);
    throw error;
  }
};

// Export default
export default {
  productSyncQueue,
  addHaravanSyncJob,
  addSapoSyncJob,
  getQueue,
  getQueuesStats,
  pauseQueue,
  resumeQueue,
  cleanQueue,
  closeAllQueues,
};

// Export types
export type { HaravanSyncJobData, SapoSyncJobData };
