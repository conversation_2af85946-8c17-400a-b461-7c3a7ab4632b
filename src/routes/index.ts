import { Router } from 'express';
import agentRoutes from './agent.routes.js';
import weaviateRoutes from './weaviate.routes.js';
import customerRoutes from './customer.routes.js';
import productRoutes from './product.routes.js';
import platformSyncRoutes from './platform-sync.routes';
import systemRoutes from './system.routes';
import testFollowupRoutes from './test-followup';

const router = Router();

// Đăng ký các routes
router.use('/agent', agentRoutes);
router.use('/weaviate', weaviateRoutes);
router.use('/customers', customerRoutes);
router.use('/products', productRoutes);
router.use('/sync', platformSyncRoutes); // Sử dụng platform-sync cho /sync
router.use('/system', systemRoutes);
router.use('/test', testFollowupRoutes); // Test routes cho follow-up

export default router;
