/**
 * Test Follow-up API - Để test follow-up job manually
 */
import { Router } from 'express';
import { createFollowUpJob, getConversationFollowUpState } from '../services/queue/follow-up.service';

const router = Router();

/**
 * Test tạo follow-up job ngay lập tức
 */
router.post('/trigger-followup', async (req, res) => {
  try {
    const { conversation_id, tenant_id, channel_id, account_id } = req.body;

    if (!conversation_id || !tenant_id || !channel_id || !account_id) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: conversation_id, tenant_id, channel_id, account_id'
      });
    }

    console.log(`🧪 Test trigger follow-up cho conversation ${conversation_id}`);

    const result = await createFollowUpJob({
      conversation_id,
      tenant_id,
      channel_id,
      account_id,
      rule_index: 0,
      reset_state: true
    });

    res.json({
      success: result.success,
      data: result.data,
      error: result.error,
      message: result.message
    });

  } catch (error: any) {
    console.error('❌ Lỗi test follow-up:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Ki<PERSON>m tra state của conversation
 */
router.get('/state/:conversation_id', async (req, res) => {
  try {
    const { conversation_id } = req.params;
    
    const state = await getConversationFollowUpState(conversation_id);
    
    res.json({
      success: true,
      data: state
    });

  } catch (error: any) {
    console.error('❌ Lỗi lấy state:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
